{"name": "linksera-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "formik": "^2.4.6", "lucide-react": "^0.453.0", "next": "^14.0.4", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4"}}