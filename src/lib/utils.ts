import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Learning points for InfoCard
export const learningPoints = [
  "How to optimize your website for better performance",
  "Best practices for SEO and content marketing",
  "Understanding analytics and user behavior",
  "Monetization strategies that actually work",
  "Building a sustainable online presence",
];

// Language options for website form
export const languageOptions = [
  { value: "en", label: "English", flag: "/flag-us.svg" },
  { value: "es", label: "Spanish", flag: "/flag-es.svg" },
  { value: "fr", label: "French", flag: "/flag-fr.svg" },
  { value: "de", label: "German", flag: "/flag-de.svg" },
  { value: "it", label: "Italian", flag: "/flag-it.svg" },
  { value: "pt", label: "Portuguese", flag: "/flag-pt.svg" },
];

// Country options for traffic source
export const countryOptions = [
  { value: "us", label: "United States", flag: "/flag-us.svg" },
  { value: "uk", label: "United Kingdom", flag: "/flag-uk.svg" },
  { value: "ca", label: "Canada", flag: "/flag-ca.svg" },
  { value: "au", label: "Australia", flag: "/flag-au.svg" },
  { value: "de", label: "Germany", flag: "/flag-de.svg" },
  { value: "fr", label: "France", flag: "/flag-fr.svg" },
  { value: "es", label: "Spain", flag: "/flag-es.svg" },
  { value: "it", label: "Italy", flag: "/flag-it.svg" },
];

// Categories for website classification
export const categories = [
  [
    { id: "technology", label: "Technology" },
    { id: "business", label: "Business" },
    { id: "health", label: "Health & Fitness" },
    { id: "lifestyle", label: "Lifestyle" },
    { id: "education", label: "Education" },
  ],
  [
    { id: "entertainment", label: "Entertainment" },
    { id: "news", label: "News & Media" },
    { id: "sports", label: "Sports" },
    { id: "travel", label: "Travel" },
    { id: "food", label: "Food & Cooking" },
  ],
  [
    { id: "fashion", label: "Fashion & Beauty" },
    { id: "finance", label: "Finance" },
    { id: "automotive", label: "Automotive" },
    { id: "real-estate", label: "Real Estate" },
    { id: "gaming", label: "Gaming" },
  ],
  [
    { id: "parenting", label: "Parenting" },
    { id: "pets", label: "Pets & Animals" },
    { id: "home-garden", label: "Home & Garden" },
    { id: "art-design", label: "Art & Design" },
    { id: "music", label: "Music" },
  ],
  [
    { id: "photography", label: "Photography" },
    { id: "science", label: "Science" },
    { id: "politics", label: "Politics" },
    { id: "religion", label: "Religion" },
    { id: "other", label: "Other" },
  ],
];
