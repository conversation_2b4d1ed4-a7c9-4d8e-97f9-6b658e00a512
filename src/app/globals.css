@import url("https://fonts.googleapis.com/css?family=DM+Sans:400,500,600,700|Inter:400,500,600,700|Manrope:600");
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --accentbase: rgba(97, 63, 221, 1);
  --background-25: rgba(253, 252, 255, 1);
  --black: rgba(0, 0, 0, 1);
  --body-b5-font-family: "Inter", Helvetica;
  --body-b5-font-size: 14px;
  --body-b5-font-style: normal;
  --body-b5-font-weight: 400;
  --body-b5-letter-spacing: 0.25px;
  --body-b5-line-height: 20px;
  --errorbase: rgba(239, 68, 68, 1);
  --foreground-40: rgba(15, 12, 27, 0.4);
  --foreground-60: rgba(15, 12, 27, 0.6);
  --foregroundbase: rgba(15, 12, 27, 1);
  --gray-500: rgba(102, 112, 133, 1);
  --heading-h2-font-family: "DM Sans", Helvetica;
  --heading-h2-font-size: 32px;
  --heading-h2-font-style: normal;
  --heading-h2-font-weight: 600;
  --heading-h2-letter-spacing: -0.25px;
  --heading-h2-line-height: 44px;
  --heading-h3-font-family: "DM Sans", Helvetica;
  --heading-h3-font-size: 24px;
  --heading-h3-font-style: normal;
  --heading-h3-font-weight: 600;
  --heading-h3-letter-spacing: 0px;
  --heading-h3-line-height: 40px;
  --heading-h5-font-family: "DM Sans", Helvetica;
  --heading-h5-font-size: 18px;
  --heading-h5-font-style: normal;
  --heading-h5-font-weight: 600;
  --heading-h5-letter-spacing: 0px;
  --heading-h5-line-height: 28px;
  --label-l1-medium-font-family: "DM Sans", Helvetica;
  --label-l1-medium-font-size: 14px;
  --label-l1-medium-font-style: normal;
  --label-l1-medium-font-weight: 500;
  --label-l1-medium-letter-spacing: 0px;
  --label-l1-medium-line-height: 20px;
  --mildbase: rgba(234, 234, 234, 1);
  --muted-60: rgba(179, 179, 179, 0.6);
  --mutedbase: rgba(179, 179, 179, 1);
  --secondary-bg100: rgba(254, 254, 255, 1);
  --shadow-sm: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  --success-10: rgba(52, 199, 89, 0.1);
  --successbase: rgba(52, 199, 89, 1);
  --text-md-normal-font-family: "Inter", Helvetica;
  --text-md-normal-font-size: 16px;
  --text-md-normal-font-style: normal;
  --text-md-normal-font-weight: 400;
  --text-md-normal-letter-spacing: 0px;
  --text-md-normal-line-height: 24px;
  --uicard: rgba(255, 255, 255, 1);
  --uiprimary: rgba(5, 74, 218, 1);
  --white: rgba(255, 255, 255, 1);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .all-\[unset\] {
    all: unset;
  }
}