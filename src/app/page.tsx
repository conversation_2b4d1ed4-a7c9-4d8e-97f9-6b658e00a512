import { DataTable } from "@/components/DataTable";
import Link from "next/link";
import { GoPlus } from "react-icons/go";

export default function Home() {
  return (
    <div className="p-6">
      <h1 className="text-2xl mb-14 font-semibold">All Websites</h1>
      <div className="mt- mb-6">
        <Link href="/add-website">
          <button className="flex gap-2 bg-[var(--primary)] text-sm justify-center cursor-pointer text-white h-9 w-[228px] rounded-xl items-center">
            <GoPlus className="w-5 h-5" /> <span>Add Website</span>
          </button>
        </Link>
      </div>
      <DataTable />
    </div>
  );
}
