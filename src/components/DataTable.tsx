import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FaLaptop,
  FaGamepad,
  FaMobileAlt,
  FaMusic,
  FaFilm,
  FaBolt,
  FaWrench,
  FaLightbulb,
  FaBullseye,
  FaRocket,
  FaTshirt,
  FaPalette,
  FaHeart,
  FaGem,
  FaStar,
  FaUtensils,
  FaLeaf,
  FaCoffee,
  FaBirthdayCake,
  FaSeedling,
  FaPlane,
  FaMountain,
  FaUmbrellaBeach,
  FaCamera,
  FaMap,
} from "react-icons/fa";

const websites = [
  {
    id: 1,
    website: "www.example.com",
    country: "USA",
    language: "United States",
    category: "Computer & Electronics",
    otherCategory: "Entertainment",
    greyNiches: [FaLaptop, FaGamepad, FaMobileAlt, FaMusic, FaFilm],
  },
  {
    id: 2,
    website: "www.techstore.com",
    country: "Canada",
    language: "English",
    category: "Technology",
    otherCategory: "Gaming",
    greyNiches: [FaBolt, FaWrench, FaLightbulb, FaBullseye, FaRocket],
  },
  {
    id: 3,
    website: "www.fashionhub.com",
    country: "UK",
    language: "English",
    category: "Fashion & Beauty",
    otherCategory: "Lifestyle",
    greyNiches: [FaTshirt, FaPalette, FaHeart, FaGem, FaStar],
  },
  {
    id: 4,
    website: "www.foodie.com",
    country: "Australia",
    language: "English",
    category: "Food & Beverage",
    otherCategory: "Health",
    greyNiches: [FaUtensils, FaLeaf, FaCoffee, FaBirthdayCake, FaSeedling],
  },
  {
    id: 5,
    website: "www.travelblog.com",
    country: "Germany",
    language: "German",
    category: "Travel & Tourism",
    otherCategory: "Adventure",
    greyNiches: [FaPlane, FaMountain, FaUmbrellaBeach, FaCamera, FaMap],
  },
];

export function DataTable() {
  return (
    <Table>
      <TableHeader className="[&_tr]:border-0">
        <TableRow className="bg-[#faf8ff] border-0">
          <TableHead className="py-4 text-gray-500 text-sm">Website</TableHead>
          <TableHead className="py-4 text-gray-500 text-sm">Country</TableHead>
          <TableHead className="py-4 text-gray-500 text-sm">Language</TableHead>
          <TableHead className="py-4 text-gray-500 text-sm">Category</TableHead>
          <TableHead className="py-4 text-gray-500 text-sm">
            Other categories
          </TableHead>
          <TableHead className="py-4 text-gray-500 text-sm">
            Grey niches
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody className="[&_tr:last-child]:border-0 [&_tr]:border-0">
        {websites.map((website, index) => (
          <TableRow
            key={website.id}
            className={`border-0 cursor-pointer ${
              index % 2 === 0 ? "bg-white" : "bg-[#faf8ff]"
            }`}
          >
            <TableCell className="py-4 text-gray-500 text-sm">
              {website.website}
            </TableCell>
            <TableCell className="py-4 text-gray-500 text-sm">
              {website.country}
            </TableCell>
            <TableCell className="py-4 text-gray-500 text-sm">
              {website.language}
            </TableCell>
            <TableCell className="py-4 text-gray-500 text-sm">
              {website.category}
            </TableCell>
            <TableCell className="py-4 text-gray-500 text-sm">
              {website.otherCategory}
            </TableCell>
            <TableCell className="py-4">
              <div className="flex gap-2">
                {website.greyNiches.map((IconComponent, iconIndex) => (
                  <span key={iconIndex} className="text-base text-gray-600">
                    <IconComponent />
                  </span>
                ))}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
      {/* <TableFooter>
        <TableRow>
          <TableCell colSpan={5}>Total Websites</TableCell>
          <TableCell className="text-right">{websites.length}</TableCell>
        </TableRow>
      </TableFooter> */}
    </Table>
  );
}
